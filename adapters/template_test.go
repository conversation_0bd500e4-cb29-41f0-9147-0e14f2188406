package adapters_test

import (
	"encoding/json"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

const jsonBlob = `{
	"id": "20",
    "style": "Modern",
    "color_scheme": "Bold",
    "name": "Modern Geometry",
    "description": "This modern bathroom design is inspired by the vibrant, bold geometries of Mexico", 
    "atmosphere": "geometric, bold, edgy",
    "color_palette": "black and white, matte black hardware, wood vanity",
    "material_palette": "handmade tile, geometric tile, oval elogated mirrors, dark wood vanity, matte black fixtures",
    "inspiration": "bold geometries of mexico",
    "plumbing_brand": "Delta",
    "lighting_brand": "Elegant Lighting",
    "vanity_brand": "James Martin Vanities",
    "toilet_brand": "Duravit",
    "vanity_storage": "wall mounted with drawers",
    "materials": {
        "id": "20",
        "tub": "",
        "tags": 20,
        "paint": "505c9c7c-4432-43c1-95fd-4650f8e4b775",
        "faucet": "",
        "mirror": "9ea7495d-4a33-4cc0-8832-d2b3545f0950",
        "toilet": "bed4b456-1058-4332-ac4b-ef40dcad5dfd",
        "vanity": "",
        "shelves": "33c3c4c9-4d57-4ae7-895b-00c80630da2c",
        "tubDoor": "",
        "lighting": "92fce210-b77d-49d6-8051-3b347917dc73",
        "robeHook": "97d986a6-d8f8-4f42-a04b-5f536f035304",
        "towelBar": "acd88e3b-3f58-4a33-8be0-86f6e8cb95a3",
        "tpHolder": "d8cfbb8b-c2a6-4bfd-a473-cf3a87ab4b1f",
        "wallTile": "135f1c09-8525-4e55-9997-cc263278ea72",
        "alcoveTub": "f976f7e6-da92-4236-9b53-ff3abc54d1eb",
        "floorTile": "ab07d14c-5b4e-44aa-96ba-1ccf3d901743",
        "towelRing": "be3e7f4d-ace2-443d-9bdc-a2601471d193",
        "tubFiller": "6608a61f-f005-410b-9efd-e6336e58a854",
        "faucetDict": {
            "24": "840eff26-6c5d-4467-b274-8c100b349cfd",
            "30": "045aef8d-0d33-4389-ac88-8a0635f611bd",
            "36": "045aef8d-0d33-4389-ac88-8a0635f611bd",
            "60": "045aef8d-0d33-4389-ac88-8a0635f611bd"
        },
        "vanityDict": {
            "24": "09d3ceb2-85fd-4e7b-86e2-3f9a97d9861f",
            "30": "9d8e170f-e0f1-4ba0-a609-e530b349195c",
            "36": "9de10ac3-166c-41fb-ae22-db8290e5ba6d",
            "60": "2a279059-d531-498d-8258-2171f1827160"
        },
        "showerGlass": "",
        "tubDoorFixed": "f0529d4f-5392-4ed1-91de-0b48ec31c14f",
        "showerWallTile": "135f1c09-8525-4e55-9997-cc263278ea72",
        "tubDoorSliding": "cd5af25c-5cb3-473e-ab7c-036fead0f041",
        "freestandingTub": "397d68a2-1c10-4bb4-96cc-145d0ef19e64",
        "showerFloorTile": "ab07d14c-5b4e-44aa-96ba-1ccf3d901743",
        "isTubDoorVisible": false,
        "showerGlassFixed": "ab32ea6f-a461-4f88-9910-4d13c722b951",
        "showerSystemFull": "8eee8bd7-21a9-4531-ae10-19af150a9980",
        "wallTilePlacement": "HalfWall",
        "showerGlassSliding": "197e7b0e-a554-4483-b956-e49dc80ef592",
        "showerSystemShower": "d16fcd40-cb89-4b9a-80af-ce294ac0f3e8",
        "wallpaperPlacement": "None",
        "isShowerGlassVisible": false
    },
    "render_priority": 1,
    "image_url": "https://cdn.arcstudio.ai/assets/Template20_ModernGeometry.webp",
    "highlighted_brand_urls": [
        "https://cdn.arcstudio.ai/assets/bedrosians-logo.pngImage preview",
        "https://cdn.arcstudio.ai/assets/behr-logo.pngImage preview",
        "https://cdn.arcstudio.ai/assets/kohler-logo-black.pngImage preview",
        "https://cdn.arcstudio.ai/assets/signature-hardware-logo.pngImage preview"
    ]
}`

func Test_FromJsonBlob(t *testing.T) {
	var template adapters.Template
	err := json.Unmarshal([]byte(jsonBlob), &template)
	require.NoError(t, err)
	assert.Equal(t, "20", template.ID)
}

func TestTemplate_AlignId(t *testing.T) {
	t.Run("should set ID when template ID is empty", func(t *testing.T) {
		template := adapters.Template{}
		expectedId := uuid.New()

		err := template.AlignId(expectedId.String())

		require.NoError(t, err)
		assert.Equal(t, expectedId.String(), template.ID)
	})

	t.Run("should set ID when template ID is nil", func(t *testing.T) {
		template := adapters.Template{ID: ""}
		expectedId := uuid.New()

		err := template.AlignId(expectedId.String())

		require.NoError(t, err)
		assert.Equal(t, expectedId.String(), template.ID)
	})

	t.Run("should succeed when template ID matches URL ID", func(t *testing.T) {
		templateId := uuid.New()
		template := adapters.Template{ID: templateId.String()}

		err := template.AlignId(templateId.String())

		require.NoError(t, err)
		assert.Equal(t, templateId.String(), template.ID)
	})

	t.Run("should return error when template ID does not match URL ID", func(t *testing.T) {
		templateId := uuid.New()
		differentId := uuid.New()
		template := adapters.Template{ID: templateId.String()}

		err := template.AlignId(differentId.String())

		require.Error(t, err)
		assert.Contains(t, err.Error(), "template ID mismatch")
		assert.Equal(t, templateId.String(), template.ID) // Should remain unchanged
	})

	t.Run("should return error for invalid UUID", func(t *testing.T) {
		template := adapters.Template{}

		err := template.AlignId("invalid-uuid")

		require.Error(t, err)
		assert.Contains(t, err.Error(), "invalid template UUID")
	})
}

func TestToUsecaseTemplate(t *testing.T) {
	templateId := uuid.New()
	vanityId1 := uuid.New()
	vanityId2 := uuid.New()
	faucetId1 := uuid.New()
	faucetId2 := uuid.New()

	t.Run("should convert valid template successfully", func(t *testing.T) {
		adapterTemplate := adapters.Template{
			ID:              templateId.String(),
			Name:            "Test Template",
			ColorScheme:     usecases.Neutral,
			Style:           usecases.Modern,
			Description:     "A test template",
			Inspiration:     "Test inspiration",
			Atmosphere:      "cozy, modern",
			ColorPalette:    "#FFFFFF, #000000",
			MaterialPalette: "wood, metal",
			Materials: adapters.Materials{
				VanityDict: map[string]uuid.UUID{
					"24": vanityId1,
					"36": vanityId2,
				},
				FaucetDict: map[string]uuid.UUID{
					"24": faucetId1,
					"36": faucetId2,
				},
			},
		}

		usecaseTemplate, err := adapters.ToUsecaseTemplate(adapterTemplate)

		require.NoError(t, err)
		assert.Equal(t, templateId, usecaseTemplate.ID)
		assert.Equal(t, "Test Template", usecaseTemplate.Name)
		assert.Equal(t, usecases.Neutral, usecaseTemplate.ColorScheme)
		assert.Equal(t, usecases.Modern, usecaseTemplate.Style)
		assert.Equal(t, "A test template", usecaseTemplate.Description)
		assert.Equal(t, []string{"cozy", "modern"}, usecaseTemplate.Atmosphere)

		// Check vanity scaling options
		assert.Len(t, usecaseTemplate.VanityScalingOptions, 2)
		assert.Equal(t, vanityId1, usecaseTemplate.VanityScalingOptions[24].VanityProductID)
		assert.Equal(t, faucetId1, usecaseTemplate.VanityScalingOptions[24].FaucetProductID)
		assert.Equal(t, vanityId2, usecaseTemplate.VanityScalingOptions[36].VanityProductID)
		assert.Equal(t, faucetId2, usecaseTemplate.VanityScalingOptions[36].FaucetProductID)
	})

	t.Run("should return error for empty ID", func(t *testing.T) {
		adapterTemplate := adapters.Template{
			ID:   "",
			Name: "Test Template",
		}

		_, err := adapters.ToUsecaseTemplate(adapterTemplate)

		require.Error(t, err)
		assert.Equal(t, usecases.ErrInvalidPayload, err)
	})

	t.Run("should return error for mismatched vanity/faucet dict lengths", func(t *testing.T) {
		adapterTemplate := adapters.Template{
			ID:   templateId.String(),
			Name: "Test Template",
			Materials: adapters.Materials{
				VanityDict: map[string]uuid.UUID{
					"24": vanityId1,
				},
				FaucetDict: map[string]uuid.UUID{
					"24": faucetId1,
					"36": faucetId2,
				},
			},
		}

		_, err := adapters.ToUsecaseTemplate(adapterTemplate)

		require.Error(t, err)
		assert.Equal(t, usecases.ErrInvalidPayload, err)
	})
}

func TestFromUsecaseTemplate(t *testing.T) {
	templateId := uuid.New()
	vanityId1 := uuid.New()
	vanityId2 := uuid.New()
	faucetId1 := uuid.New()
	faucetId2 := uuid.New()
	showerFloorTileId := uuid.New()
	wallTileId := uuid.New()

	t.Run("should convert usecase template successfully", func(t *testing.T) {
		usecaseTemplate := usecases.Template{
			ID:                 templateId,
			Name:               "Test Template",
			ColorScheme:        usecases.Neutral,
			Style:              usecases.Modern,
			Description:        "A test template",
			Inspiration:        "Test inspiration",
			Atmosphere:         []string{"cozy", "modern"},
			ColorPalette:       []string{"#FFFFFF", "#000000"},
			MaterialPalette:    []string{"wood", "metal"},
			ShowerFloorTile:    &showerFloorTileId,
			WallTile:           &wallTileId,
			WallTilePlacement:  usecases.HalfWall,
			WallpaperPlacement: usecases.NoWallpaper,
			VanityScalingOptions: map[int]usecases.VanityScalingOption{
				24: {VanityProductID: vanityId1, FaucetProductID: faucetId1},
				36: {VanityProductID: vanityId2, FaucetProductID: faucetId2},
			},
		}

		adapterTemplate := adapters.FromUsecaseTemplate(usecaseTemplate)

		assert.Equal(t, templateId.String(), adapterTemplate.ID)
		assert.Equal(t, "Test Template", adapterTemplate.Name)
		assert.Equal(t, usecases.Neutral, adapterTemplate.ColorScheme)
		assert.Equal(t, usecases.Modern, adapterTemplate.Style)
		assert.Equal(t, "A test template", adapterTemplate.Description)
		assert.Equal(t, "cozy, modern", adapterTemplate.Atmosphere)
		assert.Equal(t, showerFloorTileId, adapterTemplate.Materials.ShowerFloorTile)
		assert.Equal(t, wallTileId, adapterTemplate.Materials.WallTile)
		assert.Equal(t, usecases.HalfWall, adapterTemplate.Materials.WallTilePlacement)
		assert.Equal(t, usecases.NoWallpaper, adapterTemplate.Materials.WallpaperPlacement)

		// Check vanity/faucet dicts
		assert.Len(t, adapterTemplate.Materials.VanityDict, 2)
		assert.Len(t, adapterTemplate.Materials.FaucetDict, 2)
		assert.Equal(t, vanityId1, adapterTemplate.Materials.VanityDict["24"])
		assert.Equal(t, faucetId1, adapterTemplate.Materials.FaucetDict["24"])
		assert.Equal(t, vanityId2, adapterTemplate.Materials.VanityDict["36"])
		assert.Equal(t, faucetId2, adapterTemplate.Materials.FaucetDict["36"])
	})
}

func TestTemplate_ConversionRoundTrip(t *testing.T) {
	templateId := uuid.New()
	vanityId1 := uuid.New()
	faucetId1 := uuid.New()

	t.Run("should maintain data integrity through round trip conversion", func(t *testing.T) {
		originalTemplate := adapters.Template{
			ID:              templateId.String(),
			Name:            "Test Template",
			ColorScheme:     usecases.Neutral,
			Style:           usecases.Modern,
			Description:     "A test template",
			Inspiration:     "Test inspiration",
			Atmosphere:      "cozy, modern",
			ColorPalette:    "#FFFFFF, #000000",
			MaterialPalette: "wood, metal",
			Materials: adapters.Materials{
				VanityDict: map[string]uuid.UUID{
					"24": vanityId1,
				},
				FaucetDict: map[string]uuid.UUID{
					"24": faucetId1,
				},
			},
		}

		// Convert to usecase and back
		usecaseTemplate, err := adapters.ToUsecaseTemplate(originalTemplate)
		require.NoError(t, err)

		convertedBack := adapters.FromUsecaseTemplate(usecaseTemplate)

		// Verify key fields are preserved
		assert.Equal(t, originalTemplate.ID, convertedBack.ID)
		assert.Equal(t, originalTemplate.Name, convertedBack.Name)
		assert.Equal(t, originalTemplate.ColorScheme, convertedBack.ColorScheme)
		assert.Equal(t, originalTemplate.Style, convertedBack.Style)
		assert.Equal(t, originalTemplate.Description, convertedBack.Description)
		assert.Equal(t, originalTemplate.Inspiration, convertedBack.Inspiration)
		assert.Equal(t, originalTemplate.Atmosphere, convertedBack.Atmosphere)
		assert.Equal(t, originalTemplate.ColorPalette, convertedBack.ColorPalette)
		assert.Equal(t, originalTemplate.MaterialPalette, convertedBack.MaterialPalette)
		assert.Equal(t, len(originalTemplate.Materials.VanityDict), len(convertedBack.Materials.VanityDict))
		assert.Equal(t, len(originalTemplate.Materials.FaucetDict), len(convertedBack.Materials.FaucetDict))
		assert.Equal(t, originalTemplate.Materials.VanityDict["24"], convertedBack.Materials.VanityDict["24"])
		assert.Equal(t, originalTemplate.Materials.FaucetDict["24"], convertedBack.Materials.FaucetDict["24"])
	})
}
