package usecases

import (
	"context"
	"log/slog"
)

type TemplateCreater struct {
	templateRepo templateRepository
	logger       *slog.Logger
}

func NewTemplateCreater(templateRepo templateRepository, logger *slog.Logger) *TemplateCreater {
	if IsNil(templateRepo) {
		panic("templateRepo cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &TemplateCreater{templateRepo: templateRepo, logger: logger}
}

func (tc *TemplateCreater) CreateTemplate(ctx context.Context, presenter TemplateCreationOutcomePresenter, template Template, legacyId string) {
	if template.Name == "" {
		tc.logger.ErrorContext(ctx, "Attempt to create template with no name")
		presenter.PresentError(ErrInvalidPayload)
		return
	}

	var err error
	if template.ID, err = tc.templateRepo.InsertTemplate(ctx, template, legacyId); err != nil {
		presenter.PresentError(err)
		return
	}

	presenter.ConveySuccessWithNewResource(template)
}
