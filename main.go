package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"log/slog"
	"net/http"
	"os"

	"github.com/alexflint/go-arg"
	"github.com/google/uuid"
	"github.com/openai/openai-go"
	"github.com/santhosh-tekuri/jsonschema/v6"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/frameworks/db"
	"gitlab.com/arc-studio-ai/services/room-design/frameworks/web"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

const (
	jsonSchemaFilename        = "room-design.schema.json"
	defaultRoomLayoutFilename = "default_room_layout.json"
)

type Command string

const (
	CmdSrv         Command = "srv"
	CmdRt          Command = "roundtrip"
	CmdImportTmpls Command = "import-templates"
)

type args struct {
	PgHost     string `arg:"env" default:"alpha-database.cin8ix3jru5g.us-west-2.rds.amazonaws.com"`
	PgDatabase string `arg:"env" default:"alpha-prototype"`
	Cmd        string `arg:"positional" default:"srv"`
}

func main() {
	ctx := context.Background()
	logger := setupLogger()
	schema := controllers.Schema(jsonSchemaFilename)

	defaultRoomLayout, err := os.ReadFile(defaultRoomLayoutFilename)
	if err != nil {
		log.Fatalf("Error reading default room layout JSON file: %v", err)
	}

	var args args
	arg.MustParse(&args)

	pgPassword, err := db.GetPostgresPassword(ctx)
	if err != nil {
		log.Fatal(err)
	}

	switch Command(args.Cmd) {
	case CmdSrv:
		// Initialize both databases for the server
		pgDb := setupDb(ctx, logger, args, pgPassword)
		defer pgDb.Close()

		textGenerator := gateways.NewOpenAI(openai.NewClient())
		cmdController := controllers.NewDesignMutationController(pgDb, textGenerator, logger)
		designQueryController := controllers.NewDesignAccessController(pgDb, logger)

		relDb := db.NewRelationalDb(ctx, args.PgHost, "projects", pgPassword, logger)
		defer relDb.Close()

		presetRetriever := usecases.NewPresetRetriever(relDb)
		presetRetrievalController := controllers.NewPresetRetrievalController(presetRetriever)
		templateRetriever := usecases.NewTemplateRetriever(relDb)
		templateRetrievalController := controllers.NewTemplateRetrievalController(templateRetriever)
		templateCreater := usecases.NewTemplateCreater(relDb, logger)
		templateWriteController := controllers.NewTemplateWriteController(templateCreater, logger)

		startSrv(logger, schema, designQueryController, cmdController, defaultRoomLayout, presetRetrievalController, templateRetrievalController, templateWriteController)

	case CmdRt:
		// Initialize both databases for designs roundtrip
		pgDb := setupDb(ctx, logger, args, pgPassword)
		defer pgDb.Close()

		textGenerator := gateways.NewOpenAI(openai.NewClient())
		designQueryController := controllers.NewDesignAccessController(pgDb, logger)

		relDb := db.NewRelationalDb(ctx, args.PgHost, "projects", pgPassword, logger)
		defer relDb.Close()

		designCreator := usecases.NewDesignCreater(relDb, textGenerator, logger)
		designUpdater := usecases.NewDesignUpdater(relDb)
		designSaver := usecases.NewDesignSaver(relDb)
		bulkDesignSaver := usecases.NewBulkDesignSaver(relDb, logger)
		designDeleter := usecases.NewDesignDeleter(relDb)
		writeController := controllers.NewDesignWriteController(
			designCreator, designUpdater, designSaver, bulkDesignSaver, designDeleter, logger)

		roundTrip(ctx, logger, designQueryController, writeController)

	case CmdImportTmpls:
		// Only initialize the RelationalDb for template import
		relDb := db.NewRelationalDb(ctx, args.PgHost, "projects", pgPassword, logger)
		defer relDb.Close()

		templateCreater := usecases.NewTemplateCreater(relDb, logger)
		templateWriteController := controllers.NewTemplateWriteController(templateCreater, logger)
		importTemplates(ctx, logger, templateWriteController)

	default:
		log.Fatalf("Unknown command: %s", args.Cmd)
	}
}

func setupLogger() *slog.Logger {
	removeTimeAttr := func(groups []string, a slog.Attr) slog.Attr {
		if a.Key == slog.TimeKey {
			return slog.Attr{}
		}
		return a
	}
	var programLevel = new(slog.LevelVar) // Info by default
	logHandler := slog.NewTextHandler(os.Stderr, &slog.HandlerOptions{
		Level: programLevel, ReplaceAttr: removeTimeAttr,
	})
	logger := slog.New(logHandler)
	slog.SetDefault(logger) // Set the global logger, since this is main.
	return logger
}

func setupDb(ctx context.Context, logger *slog.Logger, args args, pgPwd string) *gateways.Postgres {
	db := db.NewPostgres(ctx, args.PgHost, args.PgDatabase, pgPwd, logger)
	count, err := db.CountProjectsWithDesigns(ctx)
	if err != nil {
		db.Close()
		log.Fatal(err)
	}
	log.Printf("DB has %d projects with designs.\n", count)
	return db
}

func startSrv(logger *slog.Logger, schema *jsonschema.Schema,
	designQueryController *controllers.DesignAccessController, cmdController *controllers.DesignMutationController,
	defaultRoomLayout json.RawMessage, presetRetrievalController *controllers.PresetRetrievalController,
	templateRetrievalController *controllers.TemplateRetrievalController, templateWriteController *controllers.TemplateWriteController) {

	writeHandler := web.NewDesignMutationHandler(logger, schema, cmdController)
	designViewHandler := web.NewHttpGetReqHandler(logger, designQueryController)
	web.RegisterGlobalHandlers(designViewHandler)
	web.RegisterDesignHandlers("/projects/{projectId}", designViewHandler, writeHandler)
	presetViewHandler := web.NewPresetQueryHandler(logger, defaultRoomLayout, presetRetrievalController)
	web.RegisterPresetHandlers("/presets", presetViewHandler)
	templateViewHandler := web.NewTemplateQueryHandler(logger, templateRetrievalController)
	templateWriteHandler := web.NewTemplateWriteHandler(logger, templateWriteController)
	web.RegisterTemplateHandlers("/templates", templateViewHandler, templateWriteHandler)

	log.Println("Server listening on :8080")
	if err := http.ListenAndServe(":8080", nil); err != nil {
		log.Fatal(err)
	}
}

type rewriter struct {
	logger        *slog.Logger
	cmdController *controllers.DesignWriteController
}

func (r *rewriter) PresentError(err error) {
	r.logger.Error("Error fetching designs", slog.String("error", err.Error()))
}

func (r *rewriter) ConveySuccess()                                      {}
func (r *rewriter) ConveySuccessWithNewResource(design usecases.Design) {}

func (r *rewriter) PresentData(ctx context.Context, data any)                     {}
func (r *rewriter) PresentDesign(ctx context.Context, design usecases.Design)     {}
func (r *rewriter) PresentDesigns(ctx context.Context, designs []usecases.Design) {}

func (r *rewriter) PresentDesignsByProject(ctx context.Context, data map[entities.ProjectId][]usecases.Design, errors []error) {
	for projectId, designs := range data {
		if projectId == "DEFAULT" {
			continue
		}
		output := make([]adapters.Design, len(designs))
		for i, design := range designs {
			d := adapters.FromUsecaseDesign(design)
			output[i] = presenters.UsePascalCaseTilePatterns(d)
		}
		r.cmdController.SaveDesignsForProject(ctx, projectId, output, r)
	}
}

func roundTrip(ctx context.Context, logger *slog.Logger, queryController *controllers.DesignAccessController, cmdController *controllers.DesignWriteController) {
	projectIds, err := queryController.IdsOfProjectsWithDesigns(ctx)
	if err != nil {
		log.Fatal(err)
	}
	rewriter := &rewriter{logger: logger, cmdController: cmdController}
	queryController.FetchDesignsForMultipleProjects(ctx, projectIds, rewriter)
}

type templateSyncer struct {
	logger     *slog.Logger
	controller *controllers.TemplateWriteController
}

func (t *templateSyncer) PresentError(err error) {
	t.logger.Error("Error saving template", slog.String("error", err.Error()))
}

func (t *templateSyncer) ConveySuccessWithNewResource(template usecases.Template) {
	t.logger.Info("Successfully saved template", slog.String("name", template.Name), slog.String("id", template.ID.String()))
}

func fetchTemplatesFromAPI(ctx context.Context) ([]adapters.Template, error) {
	url := "https://api.averyapi.com/v2/templates?limit=20"

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch templates from API: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned non-200 status: %s", resp.Status)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var apiResponse struct {
		Data []adapters.Template `json:"data"`
	}

	if err := json.Unmarshal(body, &apiResponse); err != nil {
		return nil, fmt.Errorf("failed to unmarshal API response: %w", err)
	}

	return apiResponse.Data, nil
}

func importTemplates(ctx context.Context, logger *slog.Logger, templateController *controllers.TemplateWriteController) {
	// Fetch templates from the API
	templates, err := fetchTemplatesFromAPI(ctx)
	if err != nil {
		log.Fatalf("Failed to fetch templates from API: %v", err)
	}

	logger.Info("Fetched templates from API", slog.Int("count", len(templates)))

	// Save each template to the database
	syncer := &templateSyncer{logger: logger, controller: templateController}
	for _, template := range templates {
		logger.Info("Saving template", slog.String("name", template.Name))

		// Parse template ID to UUID, or generate a new one if invalid
		templateUUID, err := uuid.Parse(template.ID)
		if err != nil {
			// Generate a new UUID for templates with non-UUID IDs
			templateUUID = uuid.New()
			logger.Info("Generated new UUID for template", slog.String("originalId", template.ID), slog.String("newUUID", templateUUID.String()))
		}

		templateController.SaveTemplate(ctx, templateUUID, template, syncer)
	}

	logger.Info("Template sync completed")
}
